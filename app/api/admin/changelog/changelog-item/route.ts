import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { deleteValue } from "@/server/kv/redis-upstash.server";
import { checkAuthAdmin } from "@/server/auth/check-auth-admin";
import { getKVKeyChangelogHeads } from "@/lib/utils";

export async function POST(req: Request) {
	if (!checkAuthAdmin()) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}

	return NextResponse.json({ status: 200, message: "ok" });
}
